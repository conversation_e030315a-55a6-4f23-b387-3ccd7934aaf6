package main

import (
	"context"
	"crypto/tls"
	"fmt"                           // [NEW]
	"log"
	"net/http"
	"time"

	"Golang/SystemSupport/internal/config"
	"Golang/SystemSupport/internal/httpserver"
	"Golang/SystemSupport/internal/mqttclient"
	"Golang/SystemSupport/internal/sign"
	"Golang/SystemSupport/internal/store/influx"
	"Golang/SystemSupport/internal/supportsvc"

	mqtt "github.com/eclipse/paho.mqtt.golang"

	// [NEW] Wails
	"github.com/wailsapp/wails/v2"
	"github.com/wailsapp/wails/v2/pkg/assetserver"
	wruntime "github.com/wailsapp/wails/v2/pkg/runtime"

	// Đừng import "runtime" chuẩn ở đây để tránh nhầm với wruntime.
	// Nếu cần NumCPU, bạn có thể import runtime chuẩn và alias nó là srt:
	// srt "runtime"
)

// === Giữ nguyên adapter cũ ===

// querierAdapter adapts influx.Querier to supportsvc.Querier
// (bỏ qua bucket vì Flux tự chỉ ra bucket trong query)
type querierAdapter struct{ q influx.Querier }

func (qa *querierAdapter) Query(ctx context.Context, org, bucket, flux string) ([]map[string]any, error) {
	return qa.q.Query(ctx, org, flux)
}
func (qa *querierAdapter) Close() error { qa.q.Close(); return nil }

// === [NEW] UIAPI để bind sang React + giữ ctx để emit event ===
type UIAPI struct{ ctx context.Context }

func (a *UIAPI) Startup(ctx context.Context) { a.ctx = ctx }
func (a *UIAPI) PushStatus(s string)         { wruntime.EventsEmit(a.ctx, "status", s) }

// === [NEW] biến toàn cục để shutdown gọn ===
var (
	httpSrv     *http.Server
	mqttHandle  *mqttclient.Handle
	svc         *supportsvc.Service
	influxQ     influx.Querier
	cancelSrv   context.CancelFunc
	uiApp       = &UIAPI{} // bind sang UI
)

// === [NEW] gom code khởi động server vào 1 hàm ===
func runServer(ctx context.Context) error {
	// 1) Load config
	cfg, err := config.Load("config.json")
	if err != nil {
		wruntime.LogWarning(ctx, "config load warning: "+err.Error())
	}

	// 2) Signer + HTTP handler + TLS
	signer, err := sign.NewEd25519SignerFromPEM(sign.DefaultEd25519PrivPEM)
	if err != nil {
		return fmt.Errorf("init signer error: %w", err)
	}
	handler := httpserver.BuildHandler(signer)
	tlsCfg := httpserver.TLSConfigFromEmbedded()

	// 3) MQTT
	if cfg.MQTT != nil && cfg.MQTT.Host != "" && cfg.MQTT.Port != 0 {
		mqttHandle, err = mqttclient.Start(ctx, cfg.MQTT)
		if err != nil {
			return fmt.Errorf("mqtt start error: %w", err)
		}
	} else {
		wruntime.LogInfo(ctx, "MQTT disabled or not configured")
	}

	// 4) Influx (tùy chọn)
	if cfg.Influx.Enable {
		influxQ = influx.NewV2(influx.V2Config{
			URL: cfg.Influx.URL, Token: cfg.Influx.Token, Org: cfg.Influx.Org,
		})
	}

	// 5) HTTPS serve
	addr := "127.0.0.1:8443"
	ln, err := tls.Listen("tcp", addr, tlsCfg)
	if err != nil {
		return fmt.Errorf("listen tls: %w", err)
	}
	httpSrv = &http.Server{Handler: handler, TLSConfig: tlsCfg}
	wruntime.LogInfo(ctx, "Go server on https://"+addr)
	go func() {
		if err := httpSrv.Serve(ln); err != nil && err != http.ErrServerClosed {
			wruntime.LogError(ctx, "http serve: "+err.Error())
			wruntime.EventsEmit(ctx, "status", "Server error: "+err.Error())
		}
	}()

	// 6) Support service + handlers (khi có MQTT)
	if mqttHandle != nil {
		var mqttCli mqtt.Client
		if c := mqttHandle.Client(); c != nil {
			mqttCli = c
		} else {
			return fmt.Errorf("mqtt client is nil")
		}

		handlers := []supportsvc.Handler{}
		if influxQ != nil {
			adapter := &querierAdapter{q: influxQ}
			dbHandler := &supportsvc.DatabaseHandler{
				MQTT:          mqttCli,
				QoS:           0,
				Retain:        false,
				DefaultOrg:    cfg.Influx.Org,
				DefaultBucket: cfg.Influx.DefaultBucket,
				Querier:       adapter,
				// Không giới hạn: không cắt rows, không timeout script (0)
				ScriptTimeout: 0,
				MaxRows:       0,
				Writer: &supportsvc.HTTPLineWriter{
					URL:   cfg.Influx.URL,
					Token: cfg.Influx.Token,
				},
			}
			handlers = append(handlers, dbHandler)
		} else {
			wruntime.LogInfo(ctx, "Influx disabled — DatabaseHandler not registered")
		}

		svc = supportsvc.New(mqttCli, supportsvc.Config{
			TopicSupport:  cfg.MQTT.TopicSupport,
			QoS:           0,
			Concurrency:   0, // hoặc srt.NumCPU() nếu bạn cần, nhớ alias runtime chuẩn thành srt
			PublishRetain: false,
		}, handlers...)

		if err := svc.Start(ctx); err != nil {
			return fmt.Errorf("support service start error: %w", err)
		}
	}

	// ✅ Đã sẵn sàng → báo UI
	wruntime.EventsEmit(ctx, "status", "Server is running")
	return nil
}

// === [NEW] shutdown gọn ===
func stopServer(ctx context.Context) {
	if httpSrv != nil {
		shutdownCtx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		_ = httpSrv.Shutdown(shutdownCtx)
		cancel()
	}
	if svc != nil {
		svc.Wait()
	}
	if influxQ != nil {
		influxQ.Close()
	}
	if mqttHandle != nil {
		mqttHandle.Close()
	}
	wruntime.EventsEmit(ctx, "status", "Server is stopping")
}

func main() {
	// Chạy Wails để vừa mở UI vừa host server
	if err := wails.Run(&wails.Options{
		Title: "SystemSupport",
		AssetServer: &assetserver.Options{ // [NEW] cần để mở cửa sổ desktop
			Assets: assets,
		},
		OnStartup: func(ctx context.Context) {
			// Lưu ctx cho UIAPI + cho emit event
			uiApp.Startup(ctx)

			// Tạo context riêng cho server (để OnShutdown cancel được)
			srvCtx, cancel := context.WithCancel(context.Background())
			cancelSrv = cancel

			// Khởi động server trong goroutine
			go func() {
				if err := runServer(srvCtx); err != nil {
					wruntime.LogError(ctx, "runServer error: "+err.Error())
					wruntime.EventsEmit(ctx, "status", "Server error: "+err.Error())
				}
			}()
		},
		OnShutdown: func(ctx context.Context) {
			// Hủy server context trước, để các component nhận ctx.Done()
			if cancelSrv != nil {
				cancelSrv()
			}
			// Sau đó dừng gọn các service
			stopServer(ctx)
		},
		Bind: []interface{}{
			uiApp, // [NEW] cho phép UI gọi PushStatus() (nếu bạn muốn)
		},
		// Width: 1200, Height: 800, // (tuỳ chọn)
	}); err != nil {
		log.Fatal(err)
	}
}
