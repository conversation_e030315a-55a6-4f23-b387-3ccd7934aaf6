package main

import (
	"log"

	"github.com/wailsapp/wails/v2"
	"github.com/wailsapp/wails/v2/pkg/options"
	"github.com/wailsapp/wails/v2/pkg/options/assetserver"

	"Golang/SystemSupport/internal/uiapp"
)

func main() {
	app := &uiapp.App{}

	if err := wails.Run(&options.App{
		Title: "SystemSupport",
		// 👇 QUAN TRỌNG: cấu hình AssetServer để Wails có handler/asset
		AssetServer: &assetserver.Options{
			Assets: assets, // lấy từ assets.go (embed)
		},
		OnStartup: app.Startup,        // để lấy ctx cho runtime.EventsEmit
		Bind:      []interface{}{app}, // auto-gen wailsjs
		// (tuỳ chọn) Set kích thước default để thấy cửa sổ rõ ràng:
		// Width: 1200, Height: 800,
	}); err != nil {
		log.Fatal(err)
	}
}
